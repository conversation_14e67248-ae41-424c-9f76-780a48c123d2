import moment from 'moment';
import { canCompleteAppointment, getAppointmentCompletionTooltip } from './dates';

/**
 * Demonstration script for appointment completion validation
 * This shows how the new functionality works with different appointment dates
 */

console.log('=== Appointment Completion Validation Demo ===\n');

// Sample appointment data with different dates
const sampleAppointments = [
  {
    id: 'apt-001',
    startDateTime: `${moment().format('YYYY-MM-DD')} 14:30`, // Today 2:30 PM
    client: 'patient-001',
    caregiver: 'caregiver-001',
    status: 'SCHEDULED'
  },
  {
    id: 'apt-002', 
    startDateTime: `${moment().subtract(1, 'day').format('YYYY-MM-DD')} 10:00`, // Yesterday 10:00 AM
    client: 'patient-002',
    caregiver: 'caregiver-001',
    status: 'SCHEDULED'
  },
  {
    id: 'apt-003',
    startDateTime: `${moment().add(1, 'day').format('YYYY-MM-DD')} 15:00`, // Tomorrow 3:00 PM
    client: 'patient-003',
    caregiver: 'caregiver-002',
    status: 'SCHEDULED'
  },
  {
    id: 'apt-004',
    startDateTime: `${moment().add(7, 'days').format('YYYY-MM-DD')} 11:00`, // Next week 11:00 AM
    client: 'patient-004',
    caregiver: 'caregiver-002',
    status: 'SCHEDULED'
  },
  {
    id: 'apt-005',
    startDateTime: `${moment().subtract(3, 'days').format('YYYY-MM-DD')} 09:30`, // 3 days ago 9:30 AM
    client: 'patient-005',
    caregiver: 'caregiver-003',
    status: 'SCHEDULED'
  }
];

// Test each appointment
sampleAppointments.forEach((appointment, index) => {
  const appointmentDate = moment(appointment.startDateTime, 'YYYY-MM-DD HH:mm');
  const canComplete = canCompleteAppointment(appointment.startDateTime);
  const tooltip = getAppointmentCompletionTooltip(appointment.startDateTime);
  
  console.log(`Appointment ${index + 1} (${appointment.id}):`);
  console.log(`  Date: ${appointmentDate.format('dddd, MMMM DD, YYYY [at] h:mm A')}`);
  console.log(`  Relative: ${appointmentDate.fromNow()}`);
  console.log(`  Can be completed: ${canComplete ? '✅ YES' : '❌ NO'}`);
  console.log(`  Button state: ${canComplete ? 'ENABLED' : 'DISABLED'}`);
  console.log(`  Tooltip: "${tooltip}"`);
  console.log('  ---');
});

// Edge cases
console.log('\n=== Edge Cases ===\n');

const edgeCases = [
  { label: 'Today at midnight', date: `${moment().format('YYYY-MM-DD')} 00:00` },
  { label: 'Today at 11:59 PM', date: `${moment().format('YYYY-MM-DD')} 23:59` },
  { label: 'Invalid date', date: null },
  { label: 'Empty string', date: '' },
  { label: 'Malformed date', date: 'not-a-date' }
];

edgeCases.forEach(testCase => {
  const canComplete = canCompleteAppointment(testCase.date);
  const tooltip = getAppointmentCompletionTooltip(testCase.date);
  
  console.log(`${testCase.label}:`);
  console.log(`  Can complete: ${canComplete ? '✅ YES' : '❌ NO'}`);
  console.log(`  Tooltip: "${tooltip}"`);
  console.log('  ---');
});

// Summary
console.log('\n=== Summary ===');
console.log('✅ Appointments scheduled for today or in the past CAN be marked as completed');
console.log('❌ Appointments scheduled for future dates CANNOT be marked as completed');
console.log('🔒 The Complete button will be disabled for future appointments');
console.log('💡 Users will see helpful tooltips explaining why completion is disabled');
console.log('⚠️  Invalid dates are handled gracefully with appropriate error messages');

export { sampleAppointments };

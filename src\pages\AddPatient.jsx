// components
import { Step, Step<PERSON><PERSON><PERSON>, Stepper } from "@mui/material";

// hooks
import Page from "@layout/Page";
import { useSearchParams } from "react-router-dom";
import { Box } from "@mui/material";
import styled from "styled-components";
import Widget from "@components/Widget";
import { useState } from "react";
import { text } from "@styles/global";
import AddPatientStep1 from "./AddPatientStep1";
import AddPatientStep2 from "./AddPatientStep2";
import AddPatientStep3 from "./AddPatientStep3";
import AddPatientStep4 from "./AddPatientStep4";
import { useEffect } from "react";
import { useSelector } from "react-redux";

const StyledStepperContainer = styled(Box)`
  .MuiStepLabel-label.Mui-active {
    color: ${text};
  }
}
`;
const ResponsiveLabel = styled.div`
  display: none;
  
  /* Show only on large screens (e.g., 1024px and above) */
  @media (min-width: 1024px) {
    display: inline;
  }
`;



const steps = ["Step 1", "Step 2", "Step 3", "Step 4"];

const AddPatient = () => {
  const [searchParams] = useSearchParams();
  const client_id = searchParams.get("client_id");
  const { clients } = useSelector((state) => state.users);

  const [currentPatient, setCurrentPatient] = useState(null);

  // SETPPER
  const [activeStep, setActiveStep] = useState(0);
  function gotoNextStep() {
    setActiveStep((prev) => (prev < steps.length - 1 ? prev + 1 : prev));
  }
  function gotoPreviousStep() {
    setActiveStep((prev) => (prev > 0 ? prev - 1 : prev));
  }

  useEffect(() => {
    if (clients?.length && client_id) {
      const patient_found = clients?.find((item) => item.id === client_id);
      patient_found && setCurrentPatient(patient_found);
    }
  }, [clients]);

  return (
    <>
      <Page title={client_id ? "Edit Patient" : "Add Patient"}>
        <Widget>
          <Box p={{ xs: 2, md: 4 }}>
            {/* STEPPER */}
            <StyledStepperContainer sx={{ width: "100%", mb: 3 }}>
              <Stepper activeStep={activeStep}>
                {steps.map((label, index) => {
                  const stepProps = {};
                  const labelProps = {};

                  return (
                    <Step key={label} {...stepProps}>
                      <StepLabel {...labelProps}>
                        <ResponsiveLabel>{label}</ResponsiveLabel>
                        </StepLabel>
                    </Step>
                  );
                })}
              </Stepper>
            </StyledStepperContainer>

            {/* STEP 1 */}
            {activeStep === 0 ? (
              <AddPatientStep1
                gotoNext={gotoNextStep}
                goBack={gotoPreviousStep}
                canGoBack={false}
                currentPatient={currentPatient}
                setCurrentPatient={setCurrentPatient}
              />
            ) : null}

            {/* STEP 2 */}
            {activeStep === 1 ? (
              <AddPatientStep2
                gotoNext={gotoNextStep}
                goBack={gotoPreviousStep}
                canGoBack
                currentPatient={currentPatient}
                setCurrentPatient={setCurrentPatient}
              />
            ) : null}

            {/* STEP 3 */}
            {activeStep === 2 ? (
              <AddPatientStep3
                gotoNext={gotoNextStep}
                goBack={gotoPreviousStep}
                canGoBack
                currentPatient={currentPatient}
                setCurrentPatient={setCurrentPatient}
              />
            ) : null}

            {/* STEP 4 */}
            {activeStep === 3 ? (
              <AddPatientStep4
                gotoNext={gotoNextStep}
                goBack={gotoPreviousStep}
                canGoBack
                currentPatient={currentPatient}
                setCurrentPatient={setCurrentPatient}
              />
            ) : null}
          </Box>
        </Widget>

        {/* ASSIGN SHIFT MODAL */}
        {/* <AssignShiftModal
          elemsHeight={0}
          mode="add_client"
          name={null}
          open={isScheduleOpen}
          handler={setAssignShiftOpen}
          date={moment().add(1, "days").format("YYYY-MM-DD")}
          client_options={[]}
          defaultValues={{ client: thisPatient?.id }}
          commonNurse={nurses.find((item) => item?.id === thisPatient?.assignedNurse)}
          caregiver_options={caregivers_list}
        /> */}

        {/* SCHEDULE APPOINTMENT MODAL */}
        {/* <ScheduleAppointmentModal
          data={{ name: "Caregiver 099" }}
          isVisible={isScheduleOpen}
          onCloseModal={() => setAssignShiftOpen(false)}
          caregiver_options={caregivers_list}
          client={thisPatient}
          mode="add_client"
        /> */}
      </Page>
    </>
  );
};

export default AddPatient;

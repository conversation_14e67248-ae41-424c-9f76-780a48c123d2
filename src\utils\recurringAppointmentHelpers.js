/**
 * Utility functions for managing recurring appointment series
 */

/**
 * Get all appointments in the same recurring series
 * @param {Array} appointments - All appointments
 * @param {string} seriesId - The series ID to filter by (refID)
 * @returns {Array} Appointments in the same series
 */
export const getAppointmentsBySeries = (appointments, seriesId) => {
  if (!seriesId || !appointments) return [];

  return appointments.filter(appointment =>
    appointment.refID === seriesId
  ).sort((a, b) => new Date(a.startDateTime) - new Date(b.startDateTime));
};

/**
 * Get the next appointment in a recurring series
 * @param {Array} appointments - All appointments
 * @param {string} seriesId - The series ID (refID)
 * @param {Date} fromDate - Date to search from (defaults to now)
 * @returns {Object|null} Next appointment or null if none found
 */
export const getNextAppointmentInSeries = (appointments, seriesId, fromDate = new Date()) => {
  const seriesAppointments = getAppointmentsBySeries(appointments, seriesId);

  return seriesAppointments.find(appointment =>
    new Date(appointment.startDateTime) > fromDate &&
    appointment.status === 'SCHEDULED'
  ) || null;
};

/**
 * Get the previous appointment in a recurring series
 * @param {Array} appointments - All appointments
 * @param {string} seriesId - The series ID (refID)
 * @param {Date} fromDate - Date to search from (defaults to now)
 * @returns {Object|null} Previous appointment or null if none found
 */
export const getPreviousAppointmentInSeries = (appointments, seriesId, fromDate = new Date()) => {
  const seriesAppointments = getAppointmentsBySeries(appointments, seriesId);

  // Reverse the array to get the most recent previous appointment
  const previousAppointments = seriesAppointments
    .filter(appointment => new Date(appointment.startDateTime) < fromDate)
    .reverse();

  return previousAppointments[0] || null;
};

/**
 * Get statistics for a recurring appointment series
 * @param {Array} appointments - All appointments
 * @param {string} seriesId - The series ID (refID)
 * @returns {Object} Statistics object
 */
export const getSeriesStatistics = (appointments, seriesId) => {
  const seriesAppointments = getAppointmentsBySeries(appointments, seriesId);
  
  if (seriesAppointments.length === 0) {
    return {
      total: 0,
      completed: 0,
      scheduled: 0,
      cancelled: 0,
      missed: 0,
      completionRate: 0
    };
  }
  
  const stats = seriesAppointments.reduce((acc, appointment) => {
    acc.total++;
    switch (appointment.status?.toLowerCase()) {
      case 'completed':
        acc.completed++;
        break;
      case 'scheduled':
        acc.scheduled++;
        break;
      case 'cancelled':
        acc.cancelled++;
        break;
      case 'missed':
        acc.missed++;
        break;
      default:
        break;
    }
    return acc;
  }, {
    total: 0,
    completed: 0,
    scheduled: 0,
    cancelled: 0,
    missed: 0
  });
  
  stats.completionRate = stats.total > 0 ? (stats.completed / stats.total) * 100 : 0;
  
  return stats;
};

/**
 * Get the recurring series ID from an appointment
 * @param {Object} appointment - The appointment object
 * @returns {string|null} The series ID (refID) or null if not a recurring appointment
 */
export const getRecurringSeriesId = (appointment) => {
  return appointment?.refID || null;
};

/**
 * Check if an appointment is part of a recurring series
 * @param {Object} appointment - The appointment object
 * @returns {boolean} True if part of a recurring series
 */
export const isRecurringAppointment = (appointment) => {
  return !!(appointment?.refID && appointment?.isRecurringInstance);
};

/**
 * Get the original recurrence settings for a series
 * @param {Array} appointments - All appointments
 * @param {string} seriesId - The series ID (refID)
 * @returns {Object|null} Original recurrence settings or null
 */
export const getSeriesRecurrenceSettings = (appointments, seriesId) => {
  const seriesAppointments = getAppointmentsBySeries(appointments, seriesId);
  
  if (seriesAppointments.length === 0) return null;
  
  // All appointments in a series should have the same original recurrence settings
  return seriesAppointments[0]?.originalRecurrence || null;
};

/**
 * Format recurring series information for display
 * @param {Array} appointments - All appointments
 * @param {string} seriesId - The series ID (refID)
 * @returns {Object} Formatted series information
 */
export const formatSeriesInfo = (appointments, seriesId) => {
  const seriesAppointments = getAppointmentsBySeries(appointments, seriesId);
  const stats = getSeriesStatistics(appointments, seriesId);
  const recurrenceSettings = getSeriesRecurrenceSettings(appointments, seriesId);

  if (seriesAppointments.length === 0) {
    return {
      seriesId: seriesId,
      totalAppointments: 0,
      recurrenceType: 'Unknown',
      status: 'No appointments found'
    };
  }

  const firstAppointment = seriesAppointments[0];
  const lastAppointment = seriesAppointments[seriesAppointments.length - 1];

  return {
    seriesId: seriesId,
    totalAppointments: stats.total,
    completedAppointments: stats.completed,
    scheduledAppointments: stats.scheduled,
    completionRate: Math.round(stats.completionRate),
    recurrenceType: recurrenceSettings?.frequence || 'Unknown',
    startDate: firstAppointment.startDateTime,
    endDate: lastAppointment.startDateTime,
    client: firstAppointment.client,
    caregiver: firstAppointment.caregiver,
    nurse: firstAppointment.nurse,
    serviceType: firstAppointment.serviceType
  };
};

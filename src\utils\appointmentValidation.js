import moment from 'moment';
import { collection, query, where, getDocs } from 'firebase/firestore';
import { db } from '@config/firebase.config';
import { COLLECTIONS } from '@constants/app';

/**
 * Checks if two time ranges overlap (time-only comparison)
 * @param {string} startTime1 - Start time of first range (HH:mm format)
 * @param {string} endTime1 - End time of first range (HH:mm format)
 * @param {string} startTime2 - Start time of second range (HH:mm format)
 * @param {string} endTime2 - End time of second range (HH:mm format)
 * @returns {boolean} True if time ranges overlap
 */
export const doTimeRangesOverlap = (startTime1, endTime1, startTime2, endTime2) => {
  // Parse times as moments on the same day for comparison
  const start1 = moment(startTime1, 'HH:mm');
  const end1 = moment(endTime1, 'HH:mm');
  const start2 = moment(startTime2, 'HH:mm');
  const end2 = moment(endTime2, 'HH:mm');

  // Handle cases where end time is before start time (crosses midnight)
  if (end1.isBefore(start1)) {
    end1.add(1, 'day');
  }
  if (end2.isBefore(start2)) {
    end2.add(1, 'day');
  }

  // Two ranges overlap if: start1 < end2 AND start2 < end1
  const overlaps = start1.isBefore(end2) && start2.isBefore(end1);

  return overlaps;
};

/**
 * Gets all existing appointments for a patient
 * @param {string} clientId - Patient/client ID
 * @param {string} excludeAppointmentId - Optional appointment ID to exclude from check
 * @returns {Promise<Array>} Array of existing appointments
 */
export const getExistingAppointmentsForPatient = async (clientId, excludeAppointmentId = null) => {
  try {
    if (!clientId) {
      return [];
    }

    const q = query(
      collection(db, COLLECTIONS.APPOINTMENTS),
      where('client', '==', clientId),
      where('status', '==', 'SCHEDULED')
    );

    const snapshot = await getDocs(q);

    const appointments = snapshot.docs
      .map(doc => ({ id: doc.id, ...doc.data() }))
      .filter(appointment => {
        // Exclude the current appointment if editing
        if (excludeAppointmentId && appointment.id === excludeAppointmentId) {
          return false;
        }
        return true;
      });

    return appointments;
  } catch (error) {
    console.error('Error fetching existing patient appointments:', error);
    throw error;
  }
};

/**
 * Gets all existing appointments for a caregiver
 * @param {string} caregiverId - Caregiver ID
 * @param {string} excludeAppointmentId - Optional appointment ID to exclude from check
 * @returns {Promise<Array>} Array of existing appointments
 */
export const getExistingAppointmentsForCaregiver = async (caregiverId, excludeAppointmentId = null) => {
  try {
    if (!caregiverId) {
      return [];
    }

    const q = query(
      collection(db, COLLECTIONS.APPOINTMENTS),
      where('caregiver', '==', caregiverId),
      where('status', '==', 'SCHEDULED')
    );

    const snapshot = await getDocs(q);

    const appointments = snapshot.docs
      .map(doc => ({ id: doc.id, ...doc.data() }))
      .filter(appointment => {
        // Exclude the current appointment if editing
        if (excludeAppointmentId && appointment.id === excludeAppointmentId) {
          return false;
        }
        return true;
      });

    return appointments;
  } catch (error) {
    console.error('Error fetching existing caregiver appointments:', error);
    throw error;
  }
};

/**
 * Main function to detect appointment time conflicts for both patient and caregiver
 * @param {string} clientId - Patient/client ID
 * @param {string} caregiverId - Caregiver ID
 * @param {string} date - Date in YYYY-MM-DD format
 * @param {string} startTime - Start time in HH:mm format
 * @param {string} endTime - End time in HH:mm format
 * @param {string} excludeAppointmentId - Optional appointment ID to exclude from check
 * @returns {Promise<Object>} Object with conflict status and details
 */
export const checkAppointmentTimeConflicts = async (clientId, caregiverId, date, startTime, endTime, excludeAppointmentId = null) => {
  try {
    if (!clientId || !caregiverId || !date || !startTime || !endTime) {
      return {
        hasConflict: false,
        patientConflicts: [],
        caregiverConflicts: []
      };
    }

    // Get all existing appointments for patient and caregiver
    const [patientAppointments, caregiverAppointments] = await Promise.all([
      getExistingAppointmentsForPatient(clientId, excludeAppointmentId),
      getExistingAppointmentsForCaregiver(caregiverId, excludeAppointmentId)
    ]);

    // Check for time conflicts with patient's existing appointments on the same date
    const patientConflicts = patientAppointments.filter(appointment => {
      const existingDate = moment(appointment.startDateTime, 'YYYY-MM-DD HH:mm').format('YYYY-MM-DD');
      const existingStartTime = moment(appointment.startDateTime, 'YYYY-MM-DD HH:mm').format('HH:mm');
      const existingEndTime = moment(appointment.endDateTime, 'YYYY-MM-DD HH:mm').format('HH:mm');

      // Only check for conflicts on the same date
      return existingDate === date && doTimeRangesOverlap(startTime, endTime, existingStartTime, existingEndTime);
    });

    // Check for time conflicts with caregiver's existing appointments on the same date
    const caregiverConflicts = caregiverAppointments.filter(appointment => {
      const existingDate = moment(appointment.startDateTime, 'YYYY-MM-DD HH:mm').format('YYYY-MM-DD');
      const existingStartTime = moment(appointment.startDateTime, 'YYYY-MM-DD HH:mm').format('HH:mm');
      const existingEndTime = moment(appointment.endDateTime, 'YYYY-MM-DD HH:mm').format('HH:mm');

      // Only check for conflicts on the same date
      return existingDate === date && doTimeRangesOverlap(startTime, endTime, existingStartTime, existingEndTime);
    });

    const hasConflict = patientConflicts.length > 0 || caregiverConflicts.length > 0;

    return {
      hasConflict,
      patientConflicts: patientConflicts.map(appointment => ({
        id: appointment.id,
        startTime: moment(appointment.startDateTime, 'YYYY-MM-DD HH:mm').format('HH:mm'),
        endTime: moment(appointment.endDateTime, 'YYYY-MM-DD HH:mm').format('HH:mm'),
        date: moment(appointment.startDateTime, 'YYYY-MM-DD HH:mm').format('YYYY-MM-DD')
      })),
      caregiverConflicts: caregiverConflicts.map(appointment => ({
        id: appointment.id,
        startTime: moment(appointment.startDateTime, 'YYYY-MM-DD HH:mm').format('HH:mm'),
        endTime: moment(appointment.endDateTime, 'YYYY-MM-DD HH:mm').format('HH:mm'),
        date: moment(appointment.startDateTime, 'YYYY-MM-DD HH:mm').format('YYYY-MM-DD'),
        clientId: appointment.client
      }))
    };
  } catch (error) {
    console.error('Error checking appointment time conflicts:', error);
    throw new Error(`Failed to validate appointment times: ${error.message}`);
  }
};

/**
 * Formats patient conflict error message for display
 * @param {Array} patientConflicts - Array of conflicting patient appointments
 * @returns {string} Formatted error message
 */
export const formatPatientConflictErrorMessage = (patientConflicts) => {
  if (patientConflicts.length === 0) {
    return '';
  }

  if (patientConflicts.length === 1) {
    const appointment = patientConflicts[0];
    return `This patient already has an appointment from ${appointment.startTime} to ${appointment.endTime} (on ${appointment.date}).`;
  }

  const timeRanges = patientConflicts
    .map(appointment => `${appointment.startTime}-${appointment.endTime} (${appointment.date})`)
    .join(', ');

  return `This patient already has appointments during these times: . Please choose a different time slot.`;
};

/**
 * Formats caregiver conflict error message for display
 * @param {Array} caregiverConflicts - Array of conflicting caregiver appointments
 * @returns {string} Formatted error message
 */
export const formatCaregiverConflictErrorMessage = (caregiverConflicts) => {
  if (caregiverConflicts.length === 0) {
    return '';
  }

  if (caregiverConflicts.length === 1) {
    const appointment = caregiverConflicts[0];
    return `This caregiver already has an appointment from ${appointment.startTime} to ${appointment.endTime} (on ${appointment.date}).`;
  }

  const timeRanges = caregiverConflicts
    .map(appointment => `${appointment.startTime}-${appointment.endTime} (${appointment.date})`)
    .join(', ');

  return `This caregiver already has appointments during these times: . Please choose a different time slot.`;
};

// Legacy functions for backward compatibility - these will be deprecated
// Use checkAppointmentTimeConflicts instead

/**
 * @deprecated Use checkAppointmentTimeConflicts instead
 * Legacy function for patient appointment conflict checking
 */
export const checkAppointmentConflict = async (clientId, date, startTime, endTime, excludeAppointmentId = null) => {
  try {
    if (!clientId || !startTime || !endTime) {
      return { hasConflict: false, conflictingAppointments: [] };
    }

    const patientAppointments = await getExistingAppointmentsForPatient(clientId, excludeAppointmentId);

    const patientConflicts = patientAppointments.filter(appointment => {
      const existingDate = moment(appointment.startDateTime, 'YYYY-MM-DD HH:mm').format('YYYY-MM-DD');
      const existingStartTime = moment(appointment.startDateTime, 'YYYY-MM-DD HH:mm').format('HH:mm');
      const existingEndTime = moment(appointment.endDateTime, 'YYYY-MM-DD HH:mm').format('HH:mm');

      // Only check for conflicts on the same date
      return date && existingDate === date && doTimeRangesOverlap(startTime, endTime, existingStartTime, existingEndTime);
    });

    return {
      hasConflict: patientConflicts.length > 0,
      conflictingAppointments: patientConflicts.map(appointment => ({
        id: appointment.id,
        startTime: moment(appointment.startDateTime, 'YYYY-MM-DD HH:mm').format('HH:mm'),
        endTime: moment(appointment.endDateTime, 'YYYY-MM-DD HH:mm').format('HH:mm'),
        date: moment(appointment.startDateTime, 'YYYY-MM-DD HH:mm').format('YYYY-MM-DD')
      }))
    };
  } catch (error) {
    console.error('Error checking patient appointment conflict:', error);
    throw new Error(`Failed to validate patient appointment: ${error.message}`);
  }
};

/**
 * @deprecated Use checkAppointmentTimeConflicts instead
 * Legacy function for caregiver appointment conflict checking
 */
export const checkCaregiverAppointmentConflict = async (caregiverId, date, startTime, endTime, excludeAppointmentId = null) => {
  try {
    if (!caregiverId || !startTime || !endTime) {
      return { hasConflict: false, conflictingAppointments: [] };
    }

    const caregiverAppointments = await getExistingAppointmentsForCaregiver(caregiverId, excludeAppointmentId);

    const caregiverConflicts = caregiverAppointments.filter(appointment => {
      const existingDate = moment(appointment.startDateTime, 'YYYY-MM-DD HH:mm').format('YYYY-MM-DD');
      const existingStartTime = moment(appointment.startDateTime, 'YYYY-MM-DD HH:mm').format('HH:mm');
      const existingEndTime = moment(appointment.endDateTime, 'YYYY-MM-DD HH:mm').format('HH:mm');

      // Only check for conflicts on the same date
      return date && existingDate === date && doTimeRangesOverlap(startTime, endTime, existingStartTime, existingEndTime);
    });

    return {
      hasConflict: caregiverConflicts.length > 0,
      conflictingAppointments: caregiverConflicts.map(appointment => ({
        id: appointment.id,
        startTime: moment(appointment.startDateTime, 'YYYY-MM-DD HH:mm').format('HH:mm'),
        endTime: moment(appointment.endDateTime, 'YYYY-MM-DD HH:mm').format('HH:mm'),
        date: moment(appointment.startDateTime, 'YYYY-MM-DD HH:mm').format('YYYY-MM-DD'),
        clientId: appointment.client
      }))
    };
  } catch (error) {
    console.error('Error checking caregiver appointment conflict:', error);
    throw new Error(`Failed to validate caregiver appointment: ${error.message}`);
  }
};

/**
 * @deprecated Use formatPatientConflictErrorMessage instead
 * Legacy function for formatting conflict error messages
 */
export const formatConflictErrorMessage = (conflictingAppointments) => {
  return formatPatientConflictErrorMessage(conflictingAppointments);
};

/**
 * @deprecated Use checkForExactTimeDuplicate instead
 * Legacy function for checking exact duplicates (backward compatibility)
 */
export const checkForExactDuplicate = async (clientId, caregiverId, date, startTime, endTime, excludeAppointmentId = null) => {
  return await checkForExactTimeDuplicate(clientId, caregiverId, date, startTime, endTime, excludeAppointmentId);
};

/**
 * Checks for exact time duplicate appointments (same client, caregiver, date and time)
 * @param {string} clientId - Patient/client ID
 * @param {string} caregiverId - Caregiver ID
 * @param {string} date - Date in YYYY-MM-DD format
 * @param {string} startTime - Start time in HH:mm format
 * @param {string} endTime - End time in HH:mm format
 * @param {string} excludeAppointmentId - Optional appointment ID to exclude from check
 * @returns {Promise<boolean>} True if exact time duplicate exists
 */
export const checkForExactTimeDuplicate = async (clientId, caregiverId, date, startTime, endTime, excludeAppointmentId = null) => {
  try {
    if (!clientId || !caregiverId || !date || !startTime || !endTime) {
      return false;
    }

    // Query for appointments with same client and caregiver
    const q = query(
      collection(db, COLLECTIONS.APPOINTMENTS),
      where('client', '==', clientId),
      where('caregiver', '==', caregiverId),
      where('status', '==', 'SCHEDULED')
    );

    const snapshot = await getDocs(q);

    // Check for exact time matches on the same date
    const exactTimeDuplicates = snapshot.docs
      .map(doc => ({ id: doc.id, ...doc.data() }))
      .filter(appointment => {
        // Exclude the current appointment if editing
        if (excludeAppointmentId && appointment.id === excludeAppointmentId) {
          return false;
        }

        const existingDate = moment(appointment.startDateTime, 'YYYY-MM-DD HH:mm').format('YYYY-MM-DD');
        const existingStartTime = moment(appointment.startDateTime, 'YYYY-MM-DD HH:mm').format('HH:mm');
        const existingEndTime = moment(appointment.endDateTime, 'YYYY-MM-DD HH:mm').format('HH:mm');

        return existingDate === date && existingStartTime === startTime && existingEndTime === endTime;
      });

    return exactTimeDuplicates.length > 0;
  } catch (error) {
    console.error('Error checking for exact time duplicate:', error);
    // Return false to allow appointment creation if check fails
    return false;
  }
};



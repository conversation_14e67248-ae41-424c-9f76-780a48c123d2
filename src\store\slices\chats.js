import { db } from "config/firebase.config";
import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import { collection, doc, getDoc, getDocs, orderBy, query, where } from "firebase/firestore";
import { COLLECTIONS } from "@constants/app";
import { generateNames } from "@utils/helpers";
import moment from "moment";

const initialState = {
  error: null,
  isLoading: false,
  chats: [],
  users: [],
};

// THUNK TO GET ALL CHATS
export const getAllChats = createAsyncThunk(
  "chats/getAllChats",
  async (userId, { rejectWithValue, fulfillWithValue }) => {
    if (userId) {
      try {
        const q = query(
          collection(db, COLLECTIONS.CHATS),
          where("participants", "array-contains", userId),
          orderBy("updatedAt", "desc"),
        );
        const snapshot = await getDocs(q);
        const chats = snapshot.docs.map((item) => ({ id: item.id, ...item.data() }));

        const user_ids = [...new Set(chats.map((chat) => chat.participants.filter((id) => id !== userId)).flat())];
        const usersSnapshot = await Promise.all(user_ids.map((item) => getDoc(doc(db, COLLECTIONS.USERS, item))));
        const users = usersSnapshot
          .map((item) => ({ id: item.id, ...item.data() }))
          .map((item) => ({
            ...item,
            firstName: generateNames(item?.name)?.firstName,
            lastName: generateNames(item?.name)?.lastName,
          }));

        const formatted_chats = chats.map((chat) => {
          const otherUserId = chat?.participants?.find((id) => id !== userId);
          const otherUser = users?.find((user) => user?.id === otherUserId);
          return otherUser ? { ...chat, otherUser } : chat;
        });

        return fulfillWithValue({ chats: formatted_chats, users });
      } catch (error) {
        console.log("ERROR GET CHATS", error);
        return rejectWithValue(error);
      }
    }
  },
);

const chatSlice = createSlice({
  name: "chats",
  initialState,
  reducers: {
    addNewChatAction: (state, { payload }) => {
      state.chats = [payload, ...state.chats];
      return state;
    },
    updateChatAction: (state, { payload }) => {
      const index = state.chats.findIndex((item) => item?.id === payload?.id);
      if (index !== -1) {
        state.chats[index] = {
          ...state.chats[index],
          ...payload,
        };
      }
      state.chats = state.chats.sort(
        (a, b) => moment(b?.updatedAt?.toDate()).valueOf() - moment(a?.updatedAt?.toDate()).valueOf(),
      );
      return state;
    },
    setChatsAction: (state, { payload }) => {
      state.chats = payload;
      return state;
    },
  },
  extraReducers: (builder) => {
    builder
      // GET CHATS
      .addCase(getAllChats.pending, (state) => {
        state.error = null;
        state.isLoading = true;
      })
      .addCase(getAllChats.fulfilled, (state, { payload }) => {
        state.chats = payload.chats;
        state.users = payload.users;
        state.isLoading = false;
      })
      .addCase(getAllChats.rejected, (state, { payload }) => {
        state.isLoading = false;
        state.error = payload;
      });
  },
});

export const { addNewChatAction, updateChatAction, setChatsAction } = chatSlice.actions;

export default chatSlice;

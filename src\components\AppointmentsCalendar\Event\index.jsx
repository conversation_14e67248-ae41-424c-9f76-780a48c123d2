// styled components
import { StyledEvent, EventModal } from "./style";

// components
import ModalWindow from "@components/ModalWindow";
import CloseBtn from "@components/ModalWindow/CloseBtn";

// utils
import moment from "moment";
import PropTypes from "prop-types";
import { useNavigate } from "react-router-dom";

// hooks
import { useTheme } from "styled-components";
import { useState,useRef,useEffect } from "react";
import useWindowSize from "@hooks/useWindowSize";
import { createPortal } from "react-dom";


const Event = ({ event, variant,onaddAppointment }) => {
  const [open, setOpen] = useState(false);
    const [popupOpen, setPopupOpen] = useState(false);
    
    
    
  
  const [appointmentsListOpen, setAppointmentsListOpen] = useState(false);
  const { theme } = useTheme();
  const navigate = useNavigate();
  let isEnded = moment(event.end).isBefore(moment());
  const isDesktop = useWindowSize().width >= 1280;

  const getTakenSlots = () => {
    const duration = moment.duration(moment(event.end).diff(moment(event.start)));
    return Math.ceil(duration.asMinutes() / 30);
  };

  const getTimeDisplay = () => {
    const startTime = moment(event.start).format("hh:mm A");
    const endTime = moment(event.end).format("hh:mm A");
    return `${startTime} - ${endTime}`;
  };

  const getStatusColor = (status) => {
    const statusColors = {
      SCHEDULED: "#FFA500", // Orange
      COMPLETED: "#4CAF50", // Green
      CANCELLED: "#F44336", // Red
      MISSED: "#FFEB3B", // Yellow
    };
    return statusColors[status?.toUpperCase()] || "#757575"; // Default gray
  };

  const getDisplayStatus = () => {
    // If multiple appointments, determine which status to show
    if (event.multipleAppointments && event.allAppointments) {
      const appointments = event.allAppointments;

      // Check if there are any scheduled appointments
      const hasScheduled = appointments.some((apt) => apt.status?.toUpperCase() === "SCHEDULED");

      if (hasScheduled) {
        return "SCHEDULED";
      } else {
        // If no scheduled, show the status of the first appointment
        return appointments[0]?.status || "SCHEDULED";
      }
    }

    // Single appointment or fallback
    return event.status || "SCHEDULED";
  };


  const getStatusText = (status) => {
    const statusMap = {
      SCHEDULED: "Scheduled",
      COMPLETED: "Completed",
      CANCELLED: "Cancelled",
      MISSED: "Missed",
    };
    return statusMap[status?.toUpperCase()] || "Scheduled";
  };

  const handleAppointmentClick = (appointment, e) => {
    e.stopPropagation();
   
    console.log("Handling appointment click:", appointment);
    const appointmentId = appointment?.isRecurringInstance
      ? appointment?.originalAppointmentId || appointment?.id
      : appointment?.id;
    console.log("Appointment ID:", appointmentId);

    if (appointmentId) {
      // Close modal first to ensure proper cleanup
      setAppointmentsListOpen(true);

      // Use setTimeout to ensure modal cleanup completes before navigation
      setTimeout(() => {
        navigate(`/appointments/${appointmentId}`);
      }, 100);
    }
  };

  const handleCloseAppointmentsList = (e) => {
    // Always prevent default behavior and stop propagation to prevent navigation
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }

    // Use timeout to ensure clean modal closure without triggering parent events
    setTimeout(() => {
      setAppointmentsListOpen(false);
    }, 0);
  };

  const handleEventClick = (e) => {
    setAppointmentsListOpen(true);
  };

  

  const EventContent = () => {
    if (event.type === "available") {
      // Calculate 3-month restriction dates (including current month)
      const threeMonthsFromNow = moment().add(2, "months").endOf("month");
      const eventDate = moment(event.start);

      // Don't show plus icon if the event is beyond the 3-month range
      if (eventDate.isAfter(threeMonthsFromNow)) {
        return null;
      }

      // return <i className="icon icon-plus" />;
    }

    const displayStatus = getDisplayStatus();
    const statusText = getStatusText(displayStatus);

    return (
      <>
        <span className="event-status" onClick={handleEventClick} style={{ color: getStatusColor(displayStatus)  }}>
          {statusText}
        </span>
        
      </>
    );
  };

  return (
    <StyledEvent
      className={`event event-${variant} ${isEnded ? "isEnded" : ""}`}
      type={event.type}
      mode={theme}
      slots={getTakenSlots()}
      onClick={handleEventClick}
    >
      <EventContent />
      {/* <span className="overlay" onClick={() => setOpen(true)} /> */}
      {!isDesktop && event.type !== "disabled" && (
        <ModalWindow visibilityHandler={setOpen} isVisible={open}>
          <EventModal>
            <div className="block">
              <span className="label">Status:</span>
              <span className="value" style={{ color: getStatusColor(getDisplayStatus()) }}>
                {getStatusText(getDisplayStatus())}
              </span>
            </div>
            <div className="block">
              <span className="label">Patient:</span>
              <span className="value">{event.name}</span>
            </div>
            <div className="block">
              <span className="label">Date:</span>
              <span className="value">{moment(event.start).format("dddd, MMMM D, YYYY")}</span>
            </div>
            <div className="block">
              <span className="label">Time:</span>
              <span className="value">{getTimeDisplay()}</span>
            </div>
            <div className="block">
              <span className="label">Duration:</span>
              <span className="value">
                {Math.round(moment.duration(moment(event.end).diff(moment(event.start))).asMinutes())} minutes
              </span>
            </div>
            {event.multipleAppointments && event.totalAppointments > 1 && (
              <div className="block">
                <span className="label">Total Appointments:</span>
                <span className="value">{event.totalAppointments} appointments on this day</span>
              </div>
            )}
            {event.type && (
              <div className="block">
                <span className="label">Type:</span>
                <span className="value">{event.type}</span>
              </div>
            )}
          </EventModal>
        </ModalWindow>
      )}
      {/* Appointments List Modal - Rendered via Portal to prevent event bubbling */}
      {
        appointmentsListOpen &&
        createPortal(
          <ModalWindow
            visibilityHandler={handleCloseAppointmentsList}
            isVisible={appointmentsListOpen}
            disableBackdropClick={false}
          >
            <EventModal>
              <CloseBtn handler={handleCloseAppointmentsList} />
              
              {!event.allAppointments && (
                <>
                  <div className="block">
                    <button
                      className="add-appointment-btn"
                      onClick={() => {
                        handleCloseAppointmentsList(); // Close modal
                        setPopupOpen(true); 
                        onaddAppointment(event); 
                      }}
                    >
                      + Add Appointment
                    </button>
                    <span className="label">Appointment Details</span>
                    <span className="value">{moment(event.start).format("dddd, MMMM D, YYYY")}</span>
                  </div>
                  <div className="appointments-list">
                    <div
                      className="appointment-item clickable"
                      onClick={(e) => {
                        e.stopPropagation();
                        const appointmentId = event?.isRecurringInstance 
                          ? event?.originalAppointmentId || event?.id 
                          : event?.id;
                        
                        if (appointmentId) {
                          handleCloseAppointmentsList();
                          setTimeout(() => {
                            navigate(`/appointments/${appointmentId}`);
                          }, 100);
                        }
                      }}
                    >
                      <div className="appointment-time">
                        {getTimeDisplay()}
                      </div>
                      <div className="appointment-details">
                        <div className="appointment-name">{event.name}</div>
                        {event.type && <div className="appointment-type">{event.type}</div>}
                      </div>
                      <div
                        className="appointment-status"
                        style={{
                          backgroundColor: getStatusColor(getDisplayStatus()) + "20",
                          color: getStatusColor(getDisplayStatus()),
                        }}
                      >
                        {getStatusText(getDisplayStatus())}
                      </div>
                    </div>
                  </div>
                </>
              )}

              {/* Show multiple appointments list */}
              {event.allAppointments && (
                <>
                  <div className="block">
                    <button
                     className="add-appointment-btn"
                     onClick={() => {
                      handleCloseAppointmentsList(); // Close modal
                    setPopupOpen(true); 
                     onaddAppointment(event); // Trigger add appointment action
          
                   }}
                  >
                 + Add Appointment
                 </button>
                      <span className="label">All Appointments</span>
                      <span className="value">{moment(event.start).format("dddd, MMMM D, YYYY")}</span>
                    </div>
                    <div className="appointments-list">
                      {event.allAppointments.map((appointment, index) => (
                        <div
                          key={appointment.id || index}
                          className="appointment-item clickable"
                          onClick={(e) => handleAppointmentClick(appointment, e)}
                        >
                          <div className="appointment-time">
                            {moment(appointment.start).format("hh:mm A")} - {moment(appointment.end).format("hh:mm A")}
                          </div>
                          <div className="appointment-details">
                            <div className="appointment-name">{appointment.name}</div>
                            {appointment.type && <div className="appointment-type">{appointment.type}</div>}
                          </div>
                          <div
                            className="appointment-status"
                            style={{
                              backgroundColor: getStatusColor(appointment.status) + "20",
                              color: getStatusColor(appointment.status),
                            }}
                          >
                            {appointment.status ? appointment.status.toLowerCase() : "scheduled"}
                          </div>
                        </div>
                      ))}
                    </div>
                </>
              )}
            </EventModal>
          </ModalWindow>,
          document.body,
        )}

    
    </StyledEvent>
  );
};

Event.propTypes = {
  event: PropTypes.object.isRequired,
  variant: PropTypes.oneOf(["day", "week", "month"]).isRequired,
};

export default Event;

import { db } from "config/firebase.config";
import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import { collection, doc, getDoc, getDocs, orderBy, query, where, updateDoc, deleteDoc } from "firebase/firestore";
import { COLLECTIONS } from "@constants/app";

const initialState = {
  error: null,
  isLoading: false,
  appointments: [],
};

// THUNK TO GET ALL APPOINTMENTS
export const getAllAppointments = createAsyncThunk(
  "appointments/getAllAppointments",
  async (_, { rejectWithValue, fulfillWithValue }) => {
    try {
      const snapshot = await getDocs(collection(db, COLLECTIONS.APPOINTMENTS));
      const arr = snapshot.docs.map((item) => ({ id: item.id, ...item.data() }));

      return fulfillWithValue(arr);
    } catch (error) {
      console.log("GET ALL APPOINTMENTS ", error);
      return rejectWithValue(error);
    }
  },
);

// THUNK TO GET ALL APPOINTMENTS OF NURSE
export const getAllAppointmentsOfNurse = createAsyncThunk(
  "appointments/getAllAppointments",
  async (nurseId, { rejectWithValue, fulfillWithValue }) => {
    try {
      const q = query(
        collection(db, COLLECTIONS.APPOINTMENTS),
        where("nurse", "==", nurseId),
        orderBy("createdAt", "desc"),
      );

      const snapshot = await getDocs(q);
      const arr = snapshot.docs.map((item) => ({ id: item.id, ...item.data() }));

      return fulfillWithValue(arr);
    } catch (error) {
      console.log("GET ALL APPOINTMENTS OF NURSE", error);
      return rejectWithValue(error);
    }
  },
);

// THUNK TO UPDATE APPOINTMENT STATUS (MARK AS COMPLETED)
export const updateAppointmentStatus = createAsyncThunk(
  "appointments/updateAppointmentStatus",
  async ({ appointmentId, status, endDate }, { rejectWithValue, fulfillWithValue }) => {
    try {
      const appointmentRef = doc(db, COLLECTIONS.APPOINTMENTS, appointmentId);
      const updateData = { status };

      if (endDate) {
        updateData.endDate = endDate;
      }

      await updateDoc(appointmentRef, updateData);

      return fulfillWithValue({ appointmentId, status, endDate });
    } catch (error) {
      console.error("UPDATE APPOINTMENT STATUS ERROR:", error);
      return rejectWithValue(error);
    }
  }
);

// THUNK TO DELETE APPOINTMENT (CANCEL APPOINTMENT)
export const deleteAppointment = createAsyncThunk(
  "appointments/deleteAppointment",
  async (appointmentId, { rejectWithValue, fulfillWithValue }) => {
    try {
      await deleteDoc(doc(db, COLLECTIONS.APPOINTMENTS, appointmentId));

      return fulfillWithValue(appointmentId);
    } catch (error) {
      console.error("DELETE APPOINTMENT ERROR:", error);
      return rejectWithValue(error);
    }
  }
);

const appointmentSlice = createSlice({
  name: "appointments",
  initialState,
  reducers: {
    addNewAppointmentAction: (state, { payload }) => {
      state.appointments = [payload, ...state.appointments];
      return state;
    },
    addMultipleAppointmentsAction: (state, { payload }) => {
      // payload should be an array of appointments
      if (Array.isArray(payload) && payload.length > 0) {
        console.log(`Adding ${payload.length} appointments to Redux store`);
        state.appointments = [...state.appointments, ...payload];
      } else {
        console.warn('addMultipleAppointmentsAction called with invalid payload:', payload);
      }
      return state;
    },
    updateAppointmentNextVisit: (state, { payload }) => {
      const { appointmentId, nextVisit } = payload;
      const appointmentIndex = state.appointments.findIndex(
        (appointment) => appointment.id === appointmentId
      );
      if (appointmentIndex !== -1) {
        state.appointments[appointmentIndex].nextVisit = nextVisit;
        state.appointments[appointmentIndex].lastNextVisitUpdate = new Date().toISOString();
      }
      return state;
    },
    updateAppointmentAction: (state, { payload }) => {
      const { appointmentId, ...updateData } = payload;
      const appointmentIndex = state.appointments.findIndex(
        (appointment) => appointment.id === appointmentId
      );
      if (appointmentIndex !== -1) {
        state.appointments[appointmentIndex] = {
          ...state.appointments[appointmentIndex],
          ...updateData
        };
      }
      return state;
    },
    removeAppointmentAction: (state, { payload }) => {
      state.appointments = state.appointments.filter(
        (appointment) => appointment.id !== payload
      );
      return state;
    },
  },
  extraReducers: (builder) => {
    builder
      // GET USER BY ID
      .addCase(getAllAppointments.pending, (state) => {
        state.error = null;
        state.isLoading = true;
      })
      .addCase(getAllAppointments.fulfilled, (state, { payload }) => {
        state.appointments = payload;
        state.isLoading = false;
      })
      .addCase(getAllAppointments.rejected, (state, { payload }) => {
        state.isLoading = false;
        state.error = payload;
      })
      // UPDATE APPOINTMENT STATUS
      .addCase(updateAppointmentStatus.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updateAppointmentStatus.fulfilled, (state, { payload }) => {
        const { appointmentId, status, endDate } = payload;
        const appointmentIndex = state.appointments.findIndex(
          (appointment) => appointment.id === appointmentId
        );
        if (appointmentIndex !== -1) {
          state.appointments[appointmentIndex].status = status;
          if (endDate) {
            state.appointments[appointmentIndex].endDate = endDate;
          }
        }
        state.isLoading = false;
      })
      .addCase(updateAppointmentStatus.rejected, (state, { payload }) => {
        state.isLoading = false;
        state.error = payload;
      })
      // DELETE APPOINTMENT
      .addCase(deleteAppointment.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(deleteAppointment.fulfilled, (state, { payload }) => {
        state.appointments = state.appointments.filter(
          (appointment) => appointment.id !== payload
        );
        state.isLoading = false;
      })
      .addCase(deleteAppointment.rejected, (state, { payload }) => {
        state.isLoading = false;
        state.error = payload;
      });
  },
});

export const {
  addNewAppointmentAction,
  addMultipleAppointmentsAction,
  updateAppointmentNextVisit,
  updateAppointmentAction,
  removeAppointmentAction
} = appointmentSlice.actions;

export default appointmentSlice;

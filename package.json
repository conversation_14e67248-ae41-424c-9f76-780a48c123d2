{"name": "medux", "version": "1.0.2", "private": true, "author": "merkulove <<EMAIL>> (https://1.envato.market/tf-merkulove)", "contributors": [{"name": "Lilit L.", "email": "<EMAIL>"}], "copyright": "Merkulove ( https://merkulov.design/ ). All rights reserved.", "support": "<EMAIL>", "license": "Envato License https://1.envato.market/KYbje", "dependencies": {"@dnd-kit/core": "^6.0.5", "@dnd-kit/modifiers": "^6.0.0", "@dnd-kit/sortable": "^7.0.1", "@dnd-kit/utilities": "^3.2.0", "@emotion/cache": "^11.10.3", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fontsource/roboto": "^4.5.8", "@fontsource/rubik": "^4.5.11", "@hookform/resolvers": "^5.0.1", "@mui-treasury/styles": "^1.13.1", "@mui/base": "^5.0.0-beta.11", "@mui/icons-material": "^7.0.2", "@mui/lab": "^5.0.0-alpha.108", "@mui/material": "^7.0.2", "@mui/system": "^5.10.14", "@mui/x-date-pickers": "^5.0.8", "@reduxjs/toolkit": "^1.8.3", "@south-paw/react-vector-maps": "^3.1.0", "@testing-library/jest-dom": "^5.14.1", "@testing-library/react": "^13.0.0", "@testing-library/user-event": "^13.2.1", "axios": "^1.9.0", "chart.js": "^3.9.1", "country-state-city": "^3.0.1", "firebase": "^11.6.0", "framer-motion": "^6.4.3", "fscreen": "^1.2.0", "lottie-react": "^2.3.1", "moment": "^2.29.4", "nanoid": "^4.0.0", "notistack": "^2.0.5", "polished": "^4.2.2", "prop-types": "^15.8.1", "react": "^18.2.0", "react-big-calendar": "^1.3.3", "react-bootstrap": "^2.4.0", "react-calendar": "^3.7.0", "react-cardiogram": "^1.0.0-beta.7", "react-chartjs-2": "^4.2.0", "react-countup": "^6.3.0", "react-d3-speedometer": "^1.0.2", "react-datepicker": "^4.8.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.2", "react-google-places-autocomplete": "^4.1.0", "react-grid-layout": "^1.3.4", "react-helmet": "^6.1.0", "react-hook-form": "^7.56.1", "react-icons": "^5.5.0", "react-indiana-drag-scroll": "^2.2.0", "react-lines-ellipsis": "^0.15.3", "react-number-format": "^5.0.0", "react-portal": "^4.2.2", "react-rating": "^2.0.5", "react-redux": "^8.0.2", "react-router": "^6.4.1", "react-scripts": "5.0.1", "react-select": "^5.4.0", "react-select-country-list": "^2.2.3", "react-sizeme": "^3.0.2", "react-swipeable-views": "^0.14.0", "react-transition-group": "^4.4.5", "react-use": "^17.4.0", "recharts": "^2.1.12", "rtl-detect": "^1.0.4", "styled-components": "^5.3.5", "styled-theming": "^2.2.0", "stylis-plugin-rtl": "^1", "swiper": "^8.3.0", "use-mobile-detect-hook": "^1.0.5", "uuid": "^11.1.0", "wavesurfer-react": "^2.2.2", "wavesurfer.js": "^6.2.0", "web-vitals": "^2.1.0", "zod": "^3.24.3"}, "scripts": {"start": "craco start", "build": "craco build", "eject": "react-scripts eject", "vercel:prod": "vercel --prod", "vercel:test": "vercel ."}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@craco/craco": "^6.4.5", "babel-eslint": "^10.1.0", "babel-plugin-macros": "^3.1.0", "babel-plugin-styled-components": "^2.0.7", "compression-webpack-plugin": "^10.0.0", "image-minimizer-webpack-plugin": "^3.6.1", "imagemin": "^8.0.1", "imagemin-mozjpeg": "^10.0.0", "imagemin-webp": "^7.0.0", "lodash": "^4.17.21", "react-router-dom": "^6.4.2"}}
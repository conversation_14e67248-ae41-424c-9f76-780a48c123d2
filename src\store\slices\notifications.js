import { db } from "config/firebase.config";
import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import { collection, doc, getDocs, orderBy, query, setDoc, where } from "firebase/firestore";
import { COLLECTIONS } from "@constants/app";

const initialState = {
  error: null,
  isLoading: false,
  notifications: [],
};

// THUNK TO GET ALL TASKS
export const getNotificationsOfAdmin = createAsyncThunk(
  "notifications/getNotificationsOfAdmin",
  async (_, { rejectWithValue, fulfillWithValue }) => {
    try {
      const q = query(
        collection(db, COLLECTIONS.NOTIFICATIONS),
        where("type", "==", "SENT_BY_ADMIN"),
        orderBy("createdAt", "desc"),
      );
      const snapshot = await getDocs(q);
      const arr = snapshot.docs.map((item) => ({ id: item.id, ...item.data() }));
      return fulfillWithValue(arr);
    } catch (error) {
      console.log("GET NOTIFICATIONS OF ADMIN THUNK >> ", error);
      return rejectWithValue(error);
    }
  },
);

// THUNK TO CREATE NEW TASK DOC
export const getNotificationsOfNurse = createAsyncThunk(
  "notifications/getNotificationsOfNurse",
  async (_, { rejectWithValue, fulfillWithValue }) => {
    try {
      const roles = ["ALL", "NURSE"];

      const queries = roles.map((role) => {
        const q = query(
          collection(db, COLLECTIONS.NOTIFICATIONS),
          where("role", "==", role),
          orderBy("createdAt", "desc"),
        );
        return getDocs(q);
      });
      const snapshots = await Promise.all(queries);
      const merged = snapshots.flatMap((snapshot) => snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() })));
      // merged.sort((a, b) => b.createdAt.toMillis() - a.createdAt.toMillis());
      return fulfillWithValue(merged);
    } catch (error) {
      console.log("GET NOTIFICATIONS OF NURSE THUNK >> ", error);
      return rejectWithValue(error);
    }
  },
);

const notificationSlice = createSlice({
  name: "notifications",
  initialState,
  reducers: {
    addNewNotificationAction: (state, { payload }) => {
      state.notifications = [payload, ...state.notifications];
      return state;
    },
  },
  extraReducers: (builder) => {
    builder
      // GET NOTIFICATIONS FOR ADMIN
      .addCase(getNotificationsOfAdmin.pending, (state) => {
        state.error = null;
        state.isLoading = true;
      })
      .addCase(getNotificationsOfAdmin.fulfilled, (state, { payload }) => {
        state.notifications = payload;
        state.isLoading = false;
      })
      .addCase(getNotificationsOfAdmin.rejected, (state, { payload }) => {
        state.isLoading = false;
        state.error = payload;
      })
      // GET NOTIFICATIONS FOR NURSE
      .addCase(getNotificationsOfNurse.pending, (state) => {
        state.error = null;
        state.isLoading = true;
      })
      .addCase(getNotificationsOfNurse.fulfilled, (state, { payload }) => {
        state.notifications = payload;
        state.isLoading = false;
      })
      .addCase(getNotificationsOfNurse.rejected, (state, { payload }) => {
        state.isLoading = false;
        state.error = payload;
      });
  },
});

export const { addNewNotificationAction } = notificationSlice.actions;

export default notificationSlice;

import React from "react";
import moment from "moment";
import { useNavigate } from "react-router-dom";
import { createPortal } from "react-dom";
import ModalWindow from "@components/ModalWindow";
import CloseBtn from "@components/ModalWindow/CloseBtn";

// styled components
import { EventModal } from "@components/AppointmentsCalendar/Event/style";

const MobileAppointmentsList = ({ 
  isVisible, 
  onClose, 
  date, 
  appointments = [], 
  onAddAppointment 
}) => {
  const navigate = useNavigate();

  // Get appointments for the selected date
  const dayAppointments = appointments.filter(apt => 
    moment(apt.start).format('YYYY-MM-DD') === moment(date).format('YYYY-MM-DD')
  );

  // Status handling functions (matching Event component)
  const getDisplayStatus = (appointment) => {
    if (appointment.type === "available") return "available";
    if (appointment.type === "disabled") return "disabled";
    return appointment.status || "SCHEDULED";
  };

  const getStatusText = (status) => {
    const statusMap = {
      SCHEDULED: "Scheduled",
      COMPLETED: "Completed",
      CANCELLED: "Cancelled", 
      MISSED: "Missed",
      available: "Available",
      disabled: "Unavailable"
    };
    return statusMap[status?.toUpperCase()] || statusMap[status] || "Scheduled";
  };

  const getStatusColor = (status) => {
    switch (status?.toUpperCase()) {
      case "SCHEDULED":
        return "#FFA500"; 
      case "COMPLETED":
        return "#059669"; // dark green
      case "CANCELLED":
        return "#EF4444"; // red
      case "MISSED":
        return "#F59E0B"; // amber
      default:
        return "#3B82F6"; // blue
    }
  };

  const getTimeDisplay = (appointment) => {
    const startTime = moment(appointment.start).format("h:mm A");
    const endTime = moment(appointment.end).format("h:mm A");
    return `${startTime} - ${endTime}`;
  };

  const handleAppointmentClick = (appointment, e) => {
    e.stopPropagation();
    
    const appointmentId = appointment?.isRecurringInstance
      ? appointment?.originalAppointmentId || appointment?.id
      : appointment?.id;

    if (appointmentId) {
      onClose(); // Close modal first
      setTimeout(() => {
        navigate(`/appointments/${appointmentId}`);
      }, 100);
    }
  };

  const handleAddAppointment = () => {
    onClose(); // Close appointments list modal
    onAddAppointment(); // Open add appointment modal
  };

  if (!isVisible) return null;

  return createPortal(
    <ModalWindow
      visibilityHandler={onClose}
      isVisible={isVisible}
      disableBackdropClick={false}
    >
      <EventModal>
        <CloseBtn handler={onClose} />
        
        <div className="block">
          <button
            className="add-appointment-btn"
            onClick={handleAddAppointment}
          >
            + Add Appointment
          </button>
          <span className="label">
            {dayAppointments.length > 1 ? "All Appointments" : "Appointment Details"}
          </span>
          <span className="value">
            {moment(date).format("dddd, MMMM D, YYYY")}
          </span>
        </div>

        {dayAppointments.length === 0 ? (
          <div className="appointments-list">
            <div className="appointment-item">
              <div className="appointment-details">
                <div className="appointment-name">No appointments scheduled</div>
                <div className="appointment-type">Click "Add Appointment" to schedule one</div>
              </div>
            </div>
          </div>
        ) : (
          <div className="appointments-list">
            {dayAppointments.map((appointment, index) => (
              <div
                key={appointment.id || index}
                className="appointment-item clickable"
                onClick={(e) => handleAppointmentClick(appointment, e)}
              >
                <div className="appointment-time">
                  {getTimeDisplay(appointment)}
                </div>
                <div className="appointment-details">
                  <div className="appointment-name">{appointment.name}</div>
                  {appointment.type && <div className="appointment-type">{appointment.type}</div>}
                </div>
                <div
                  className="appointment-status"
                  style={{
                    backgroundColor: getStatusColor(getDisplayStatus(appointment)) + "20",
                    color: getStatusColor(getDisplayStatus(appointment)),
                  }}
                >
                  {getStatusText(getDisplayStatus(appointment))}
                </div>
              </div>
            ))}
          </div>
        )}
      </EventModal>
    </ModalWindow>,
    document.body
  );
};

export default MobileAppointmentsList;

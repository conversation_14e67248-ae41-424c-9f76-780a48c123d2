// styling
import styled from "styled-components/macro";
import { flex, textSizes } from "@styles/vars";

const statusColors = {
  SCHEDULED: "#FFA500", // Orange
  COMPLETED: "#4CAF50", // Green
  CANCELLED: "#F44336", // Red
  MISSED: "#FFEB3B", // Yellow
};

const Wrapper = styled.div`
  display: flex;
  ${flex.center};
  border-radius: 20px;
  padding: 10px 16px;
  color: #fff;
  font-size: ${textSizes["14"]};
  gap: 10px;
  background-color: ${(props) => statusColors[props.status] || "#757575"}; // Default gray if status not found

  span {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    text-transform: capitalize;
  }
`;

const AppointmentStatus = ({ icon, status }) => {
  return (
    <Wrapper className="reminder" status={status}>
      {icon}
      <span>{status?.toLowerCase()}</span>
    </Wrapper>
  );
};

export default AppointmentStatus;

import { Box, Typography } from "@mui/material";
import { dark, light } from "@styles/vars";
import moment from "moment";
import styled from "styled-components";
import theme from "styled-theming";

const iconBg = theme("theme", {
  light: light.widgetBg,
  dark: dark.widgetBg,
});

const bg = theme("theme", {
  light: light.bodyBg,
  dark: dark.highlight,
});

const Wrapper = styled.div`
  display: flex;
  align-items: center;
  gap: 0.8rem;
  background-color: ${bg};
  padding: 0.5rem;
  border-radius: 6px;

  .icon {
    display: inline-block;
    height: 40px;
    width: 40px;
    background-color: ${iconBg};
    display: grid;
    place-items: center;
    border-radius: 99px;
  }
`;

const NotificationItem = ({ notification }) => {
  return (
    <>
      <Wrapper>
        <span className="icon">
          <i className="icon icon-bell"></i>
        </span>
        <Box sx={{ display: "flex", flexDirection: "column", maxWidth: { xs: "45%", md: "75%" } }}>
          <Typography noWrap>{notification?.title}</Typography>
          <Typography variant="caption" noWrap>
            {notification?.body}
          </Typography>
        </Box>
        <Box ml="auto" display="flex" flexDirection="column" alignItems="flex-end">
          <Typography variant="caption">{moment(notification?.createdAt?.toDate()).format("hh:mm A")}</Typography>
          <Typography variant="caption">{moment(notification?.createdAt?.toDate()).format("DD/MM/YY")}</Typography>
        </Box>
      </Wrapper>
    </>
  );
};

export default NotificationItem;

// styled components
import { <PERSON><PERSON>, UserWrapper } from "../style";

// components
import Avatar from "@ui/Avatar";

// utils
import { ClickAwayListener } from "@mui/base/ClickAwayListener";
import { useState } from "react";
import { useNavigate } from "react-router-dom";

// assets
import doc1jpg from "@assets/avatars/doc1.jpg";
import doc1webp from "@assets/avatars/doc1.jpg?as=webp";
import { useDispatch, useSelector } from "react-redux";
import ConfirmationModal from "@components/ConfirmationModal";
import { updateAuthUserAction } from "@store/slices/auth";
import { getNameInitials } from "@utils/helpers";
import useWindowSize from "@hooks/useWindowSize";

const CurrentUser = () => {
  const [open, setOpen] = useState(false);
  const handleClickAway = () => setOpen(false);
  const handleClick = () => setOpen(!open);

  const { width } = useWindowSize();
  const isDesktop = width >= 1366;
  const isTablet = width >= 768 && width < 1366;

  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { user } = useSelector((state) => state.auth);
  const [isLogoutOpen, setLogoutOpen] = useState(false);

  const firstName = user?.name?.split(" ")[0] || "";
  const lastName = user?.name?.split(" ")[1] || "";

  function onClickLogout() {
    handleClickAway();
    setLogoutOpen(true);
  }

  const src = {
    jpg: doc1jpg,
    webp: doc1webp,
  };

  return (
    <>
      <ClickAwayListener onClickAway={handleClickAway}>
        <UserWrapper sx={{ display: "flex" }}>
          {isDesktop || isTablet ? (
            <>
              <Avatar
                avatar={{ jpg: user?.photo?.url }}
                alt="avatar"
                size={40}
                initals={getNameInitials(firstName, lastName)}
              />
              <div className="info">
                <span className="h3">{user?.name}</span>

                <Menu className={open ? "visible" : ""}>
                  <button onClick={onClickLogout}>
                    <i className="icon icon-logout" /> Logout
                  </button>
                </Menu>
              </div>
              <button className="trigger" onClick={handleClick} aria-label="Show menu">
                <i className="icon icon-chevron-down" />
              </button>
            </>
          ) : (
            <>
              <button onClick={handleClick}>
                <Avatar
                  avatar={{ jpg: user?.photo?.url }}
                  alt="avatar"
                  size={40}
                  initals={getNameInitials(firstName, lastName)}
                />
              </button>

              <div className="info">
                <Menu className={open ? "visible" : ""}>
                  <button onClick={onClickLogout}>
                    <i className="icon icon-logout" /> Logout
                  </button>
                </Menu>
              </div>
            </>
          )}
        </UserWrapper>
      </ClickAwayListener>
      <ConfirmationModal
        isOpen={isLogoutOpen}
        title="Logout?"
        subtitle="Are you sure you want to logout?"
        onConfirm={() => {
          localStorage.removeItem("userId");
          dispatch(updateAuthUserAction(null));
          navigate("/login");
        }}
        onCancel={() => setLogoutOpen(false)}
        confirmText="Logout"
        cancelText="Cancel"
      />
    </>
  );
};

export default CurrentUser;
